from openai import AzureOpenAI
client = AzureOpenAI()

response = client.chat.completions.create(model='myllm',
                                          messages=[{"role":"user","content":"hi"}])
print(response.choices[0].to_json(indent=2))

import os
import json
import requests

def get_current_weather(city:str)->dict:
    """ this function can be used to fetch weather information for a given city"""
    api_key=os.environ['OPENWEATHERMAP_API_KEY']

    url = f"https://api.openweathermap.org/data/2.5/weather?q={city}&appid={api_key}"
    response = requests.get(url)
    response = response.content.decode() # bytes to string
    response = json.loads(response) # string to dict
    output = {"city":city,
              "weather":response['weather'][0]['description'],
              "temperature":response['main']['temp'],
              "unit":"kelvin"}
    return output


get_current_weather("mumbai")



