Metadata-Version: 2.3
Name: ipykernel
Version: 6.29.5
Summary: IPython Kernel for Jupyter
Project-URL: Homepage, https://ipython.org
Project-URL: Documentation, https://ipykernel.readthedocs.io
Project-URL: Funding, https://numfocus.org/donate
Project-URL: Source, https://github.com/ipython/ipykernel
Project-URL: Tracker, https://github.com/ipython/ipykernel/issues
Author-email: IPython Development Team <<EMAIL>>
License: BSD 3-Clause License
        
        Copyright (c) 2015, IPython Development Team
        
        All rights reserved.
        
        Redistribution and use in source and binary forms, with or without
        modification, are permitted provided that the following conditions are met:
        
        1. Redistributions of source code must retain the above copyright notice, this
           list of conditions and the following disclaimer.
        
        2. Redistributions in binary form must reproduce the above copyright notice,
           this list of conditions and the following disclaimer in the documentation
           and/or other materials provided with the distribution.
        
        3. Neither the name of the copyright holder nor the names of its
           contributors may be used to endorse or promote products derived from
           this software without specific prior written permission.
        
        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
        AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
        IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
        DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
        FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
        DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
        SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
        OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
        OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
License-File: LICENSE
Keywords: Interactive,Interpreter,Shell,Web
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Requires-Python: >=3.8
Requires-Dist: appnope; platform_system == 'Darwin'
Requires-Dist: comm>=0.1.1
Requires-Dist: debugpy>=1.6.5
Requires-Dist: ipython>=7.23.1
Requires-Dist: jupyter-client>=6.1.12
Requires-Dist: jupyter-core!=5.0.*,>=4.12
Requires-Dist: matplotlib-inline>=0.1
Requires-Dist: nest-asyncio
Requires-Dist: packaging
Requires-Dist: psutil
Requires-Dist: pyzmq>=24
Requires-Dist: tornado>=6.1
Requires-Dist: traitlets>=5.4.0
Provides-Extra: cov
Requires-Dist: coverage[toml]; extra == 'cov'
Requires-Dist: curio; extra == 'cov'
Requires-Dist: matplotlib; extra == 'cov'
Requires-Dist: pytest-cov; extra == 'cov'
Requires-Dist: trio; extra == 'cov'
Provides-Extra: docs
Requires-Dist: myst-parser; extra == 'docs'
Requires-Dist: pydata-sphinx-theme; extra == 'docs'
Requires-Dist: sphinx; extra == 'docs'
Requires-Dist: sphinx-autodoc-typehints; extra == 'docs'
Requires-Dist: sphinxcontrib-github-alt; extra == 'docs'
Requires-Dist: sphinxcontrib-spelling; extra == 'docs'
Requires-Dist: trio; extra == 'docs'
Provides-Extra: pyqt5
Requires-Dist: pyqt5; extra == 'pyqt5'
Provides-Extra: pyside6
Requires-Dist: pyside6; extra == 'pyside6'
Provides-Extra: test
Requires-Dist: flaky; extra == 'test'
Requires-Dist: ipyparallel; extra == 'test'
Requires-Dist: pre-commit; extra == 'test'
Requires-Dist: pytest-asyncio>=0.23.5; extra == 'test'
Requires-Dist: pytest-cov; extra == 'test'
Requires-Dist: pytest-timeout; extra == 'test'
Requires-Dist: pytest>=7.0; extra == 'test'
Description-Content-Type: text/markdown

# IPython Kernel for Jupyter

[![Build Status](https://github.com/ipython/ipykernel/actions/workflows/ci.yml/badge.svg?query=branch%3Amain++)](https://github.com/ipython/ipykernel/actions/workflows/ci.yml/badge.svg?query=branch%3Amain++)
[![Documentation Status](https://readthedocs.org/projects/ipykernel/badge/?version=latest)](http://ipykernel.readthedocs.io/en/latest/?badge=latest)

This package provides the IPython kernel for Jupyter.

## Installation from source

1. `git clone`
1. `cd ipykernel`
1. `pip install -e ".[test]"`

After that, all normal `ipython` commands will use this newly-installed version of the kernel.

## Running tests

Follow the instructions from `Installation from source`.

and then from the root directory

```bash
pytest
```

## Running tests with coverage

Follow the instructions from `Installation from source`.

and then from the root directory

```bash
pytest -vv -s --cov ipykernel --cov-branch --cov-report term-missing:skip-covered --durations 10
```

## About the IPython Development Team

The IPython Development Team is the set of all contributors to the IPython project.
This includes all of the IPython subprojects.

The core team that coordinates development on GitHub can be found here:
https://github.com/ipython/.

## Our Copyright Policy

IPython uses a shared copyright model. Each contributor maintains copyright
over their contributions to IPython. But, it is important to note that these
contributions are typically only changes to the repositories. Thus, the IPython
source code, in its entirety is not the copyright of any single person or
institution. Instead, it is the collective copyright of the entire IPython
Development Team. If individual contributors want to maintain a record of what
changes/contributions they have specific copyright on, they should indicate
their copyright in the commit message of the change, when they commit the
change to one of the IPython repositories.

With this in mind, the following banner should be used in any source code file
to indicate the copyright and license terms:

```
# Copyright (c) IPython Development Team.
# Distributed under the terms of the Modified BSD License.
```
